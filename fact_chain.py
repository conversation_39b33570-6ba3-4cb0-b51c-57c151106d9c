# chains/fact_chain.py
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnablePassthrough

class FactChain:
    def __init__(self, llm, fact_db=None):
        self.fact_db = fact_db
        self.chain = PromptTemplate.from_template("""
        你是百科领域的专家，请判断以下陈述是否准确：

        用户陈述：
        {text}
        
        参考事实：
        {reference}

        请判断：
        1. 陈述是否准确？
        2. 是否与事实一致？
        3. 如果有问题，请简要说明。

        请用如下格式输出：
        - 是否准确：是/否
        - 问题说明：...
        """) | llm

    def run(self, text):
        reference = "未找到匹配的参考事实"
        
        if self.fact_db:
            # 这里可以实现更复杂的匹配逻辑
            # 例如使用关键词提取或向量搜索找到相关事实
            keywords = self._extract_keywords(text)
            for key in keywords:
                if key in self.fact_db:
                    reference = self.fact_db[key]
                    break
                    
        return self.chain.invoke({
            "text": text,
            "reference": reference
        })
    
    # 新增：异步方法支持
    async def arun(self, text):
        reference = "未找到匹配的参考事实"
        
        if self.fact_db:
            # 这里可以实现更复杂的匹配逻辑
            # 例如使用关键词提取或向量搜索找到相关事实
            keywords = self._extract_keywords(text)
            for key in keywords:
                if key in self.fact_db:
                    reference = self.fact_db[key]
                    break
                    
        return await self.chain.ainvoke({
            "text": text,
            "reference": reference
        })
        
    def _extract_keywords(self, text):
        # 简单实现，实际可以使用更复杂的NLP方法
        # 或者使用LLM提取关键词
        words = text.lower().split()
        return [w for w in words if len(w) > 3]  # 简单过滤短词
