# app.py
from langchain_community.chat_models import ChatOpenAI
from law_chain import <PERSON><PERSON>hain
from citation_chain import Citation<PERSON>hain
from fact_chain import Fact<PERSON>hain
from router import MCPRouter, ModelControlledRouter
import asyncio
import os

# 设置OpenAI API密钥（请替换为您的实际API密钥）
os.environ["OPENAI_API_KEY"] = "your_openai_api_key_here"

# 本地法律条文数据
law_dict = {
    "第108条": "民事主体依法享有民事权利，承担民事义务。",
    "第109条": "自然人享有生命权、身体权、健康权等人格权。",
}

# 初始化 GPT-4 模型
llm = ChatOpenAI(model_name="gpt-4", temperature=0)

# 初始化各个链
law_chain = LawChain(llm, law_dict)
citation_chain = CitationChain(llm)
fact_chain = FactChain(llm)

# 原始路由器（基于规则的路由）
rule_router = MCPRouter({"law": law_chain, "citation": citation_chain, "fact": fact_chain})

# 新增：模型控制的路由器（真正的MCP实现）
model_router = ModelControlledRouter(llm, {"law": law_chain, "citation": citation_chain, "fact": fact_chain})

# 要处理的段落列表
paragraphs = [
    "根据《民法典》第108条，未成年人与他人签订合同无效。",
    "根据某某（2020）提出，现代经济需要更强的创新能力。",
    "地球绕太阳公转，公转周期为365.25天。",
]

# 原始方法：使用规则路由器异步处理所有段落
async def process_with_rule_router():
    tasks = [asyncio.create_task(rule_router.arun(paragraph)) for paragraph in paragraphs]
    results = await asyncio.gather(*tasks)
    return results

# 新增：使用模型控制路由器异步处理所有段落
async def process_with_model_router():
    tasks = [asyncio.create_task(model_router.dispatch(paragraph)) for paragraph in paragraphs]
    results = await asyncio.gather(*tasks)
    return results

# 运行并输出结果
async def main():
    # 使用规则路由器处理
    print("=== 使用规则路由器处理 ===")
    rule_results = await process_with_rule_router()
    for i, result in enumerate(rule_results):
        print(f"段落 {i+1}: {result}")
    
    print("\n=== 使用模型控制路由器处理（真正的MCP） ===")
    model_results = await process_with_model_router()
    for i, result in enumerate(model_results):
        print(f"段落 {i+1}: {result}")

if __name__ == "__main__":
    asyncio.run(main())
