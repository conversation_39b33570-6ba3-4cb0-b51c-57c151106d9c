# chains/law_chain.py
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnablePassthrough

class LawChain:
    def __init__(self, llm, law_dict):
        self.chain = PromptTemplate.from_template("""
        你是一位法律专家，请根据以下法律条文判断用户引用是否正确。

        原始法律条文：
        {reference}

        用户引用内容：
        {text}

        请判断：
        1. 用户引用是否与原文一致？
        2. 是否有理解错误、歪曲或错引？
        3. 如果有问题，请简要说明错误。

        请用如下格式输出：
        - 是否准确：是/否
        - 问题说明：...
        """) | llm
        self.law_dict = law_dict

    def run(self, text):
        # 提取条文号（如"第108条"）
        import re
        match = re.search(r'第(\d{1,3})条', text)
        if not match:
            return "未发现条文引用"

        article = f"第{match.group(1)}条"
        reference = self.law_dict.get(article, "找不到该条文，请检查是否引用错误。")

        return self.chain.run({
            "text": text,
            "reference": reference
        })
        
    # 新增：异步方法支持
    async def arun(self, text):
        # 提取条文号（如"第108条"）
        import re
        match = re.search(r'第(\d{1,3})条', text)
        if not match:
            return "未发现条文引用"

        article = f"第{match.group(1)}条"
        reference = self.law_dict.get(article, "找不到该条文，请检查是否引用错误。")

        return await self.chain.arun({
            "text": text,
            "reference": reference
        })
