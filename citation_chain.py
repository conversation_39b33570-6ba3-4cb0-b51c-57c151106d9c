# chains/citation_chain.py
from langchain.prompts import PromptTemplate
from langchain.chains import LL<PERSON>hain
from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import FAISS

class CitationChain:
    def __init__(self, llm, citation_db=None):
        self.citation_db = citation_db  # 添加引用数据库
        self.chain = LLMChain(prompt=PromptTemplate.from_template("""
        你是学术领域的专家，判断以下引用是否准确：

        用户引用：
        {text}
        
        参考文献原文：
        {reference}

        请判断：
        1. 引用是否准确？
        2. 是否与原文一致？
        3. 如果有错误，请简要描述。

        请用如下格式输出：
        - 是否准确：是/否
        - 问题说明：...
        """), llm=llm)

    def run(self, text):
        # 提取引用的作者和年份
        import re
        match = re.search(r'根据(.+?)（(\d{4})）', text)
        reference = "未找到匹配的参考文献"
        
        if match and self.citation_db:
            author = match.group(1)
            year = match.group(2)
            key = f"{author}_{year}"
            reference = self.citation_db.get(key, "未找到该参考文献")
            
        return self.chain.run({
            "text": text,
            "reference": reference
        })
        
    # 新增：异步方法支持
    async def arun(self, text):
        # 提取引用的作者和年份
        import re
        match = re.search(r'根据(.+?)（(\d{4})）', text)
        reference = "未找到匹配的参考文献"
        
        if match and self.citation_db:
            author = match.group(1)
            year = match.group(2)
            key = f"{author}_{year}"
            reference = self.citation_db.get(key, "未找到该参考文献")
            
        return await self.chain.arun({
            "text": text,
            "reference": reference
        })

class EnhancedFactChain:
    def __init__(self, llm, fact_texts=None):
        self.llm = llm
        self.embeddings = OpenAIEmbeddings()
        
        if fact_texts:
            # 创建向量数据库
            self.vector_db = FAISS.from_texts(
                fact_texts, 
                self.embeddings
            )
        else:
            self.vector_db = None
            
        self.chain = LLMChain(prompt=PromptTemplate.from_template("""
        你是百科领域的专家，请判断以下陈述是否准确：

        用户陈述：
        {text}
        
        参考事实：
        {reference}

        请判断：
        1. 陈述是否准确？
        2. 是否与事实一致？
        3. 如果有问题，请简要说明。

        请用如下格式输出：
        - 是否准确：是/否
        - 问题说明：...
        """), llm=llm)

    def run(self, text):
        reference = "未找到匹配的参考事实"
        
        if self.vector_db:
            # 使用向量搜索找到最相关的事实
            docs = self.vector_db.similarity_search(text, k=1)
            if docs:
                reference = docs[0].page_content
                    
        return self.chain.run({
            "text": text,
            "reference": reference
        })
        
    # 新增：异步方法支持
    async def arun(self, text):
        reference = "未找到匹配的参考事实"
        
        if self.vector_db:
            # 使用向量搜索找到最相关的事实
            docs = self.vector_db.similarity_search(text, k=1)
            if docs:
                reference = docs[0].page_content
                    
        return await self.chain.arun({
            "text": text,
            "reference": reference
        })
