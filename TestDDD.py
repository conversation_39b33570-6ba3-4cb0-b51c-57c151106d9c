from langchain.chains import <PERSON><PERSON>hain
from langchain.chains.router import PromptRout<PERSON><PERSON>hain
from langchain.llms import OpenAI

# 定义各子链
summarize_chain = LLMChain(llm=OpenAI(), prompt="请对下面内容进行总结：{input}")
translate_chain = LLMChain(llm=OpenAI(), prompt="请将下面内容翻译成英文：{input}")
qa_chain = LLMChain(llm=OpenAI(), prompt="请根据下面内容回答问题：{input}")

# 定义子链字典
destination_chains = {
    "summarize": summarize_chain,
    "translate": translate_chain,
    "qa": qa_chain,
}

# 定义路由prompt，让大模型来分流
router_prompt = (
    "你是一个路由分流助手，根据用户的输入内容帮我选择合适的处理方式。"
    "如果用户想要总结内容，输出 summarize。"
    "如果用户想要翻译，输出 translate。"
    "如果用户提问，输出 qa。"
    "只输出对应的关键词，不要输出其他内容。"
    "用户输入：{input}"
)

# 创建路由链
router_chain = LLMChain(llm=OpenAI(), prompt=router_prompt)
mcp = PromptRouterChain(
    router_chain=router_chain,
    destination_chains=destination_chains,
    default_chain=qa_chain  # 默认处理链，可选
)

# 调用
res = mcp.invoke({"input": "请将这段话翻译成英文"})
print(res)
