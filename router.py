# router.py
class MCPRouter:
    def __init__(self, chains):
        self.chains = chains  # dict: {"law": <PERSON><PERSON><PERSON><PERSON>(), "citation": Citation<PERSON>hain(), "fact": Fact<PERSON>hain()}

    def dispatch(self, text_segment):
        if "第" in text_segment and "条" in text_segment:  # 如果是法条引用
            return self.chains["law"].run(text_segment)
        elif "根据" in text_segment or "作者" in text_segment:  # 如果是学术引用
            return self.chains["citation"].run(text_segment)
        else:  # 默认是百科事实校对
            return self.chains["fact"].run(text_segment)
            
    # 新增：异步方法支持
    async def arun(self, text_segment):
        if "第" in text_segment and "条" in text_segment:  # 如果是法条引用
            return await self.chains["law"].arun(text_segment)
        elif "根据" in text_segment or "作者" in text_segment:  # 如果是学术引用
            return await self.chains["citation"].arun(text_segment)
        else:  # 默认是百科事实校对
            return await self.chains["fact"].arun(text_segment)

# 新增：真正的MCP实现，由模型决定路由
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain

class ModelControlledRouter:
    def __init__(self, llm, chains):
        self.llm = llm
        self.chains = chains
        
        # 创建路由决策提示模板
        self.router_prompt = PromptTemplate.from_template("""
        你是一个智能路由器。请分析以下文本，并决定应该使用哪个专门的处理链。
        
        可用的处理链有：
        - law: 处理法律条文引用和解释
        - citation: 处理学术引用和文献引证
        - fact: 处理百科事实和一般知识陈述
        
        文本: {text}
        
        请仔细分析文本的内容、结构和意图，然后只回答一个词：law, citation 或 fact
        """)
        
        # 创建路由决策链
        self.router_chain = LLMChain(llm=llm, prompt=self.router_prompt)
    
    async def dispatch(self, text_segment):
        # 让模型决定使用哪个链
        chain_type = await self.router_chain.arun({"text": text_segment})
        chain_type = chain_type.strip().lower()
        
        # 处理可能的多余输出，只保留链类型名称
        if "law" in chain_type:
            chain_type = "law"
        elif "citation" in chain_type:
            chain_type = "citation"
        elif "fact" in chain_type:
            chain_type = "fact"
        else:
            # 默认回退到fact链
            chain_type = "fact"
            
        # 调用选定的链的异步方法
        return await self.chains[chain_type].arun(text_segment)
